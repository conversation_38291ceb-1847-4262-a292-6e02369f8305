import os
import torch
import hydra
from plyfile import PlyData
from src.utils import init_config
from src.data import Data, InstanceData
from src.utils.color import to_float_rgb
from src.datasets.kitti360_config import *
from src.transforms import NAGRemoveKeys, instantiate_datamodule_transforms
from torch_geometric.nn.pool.consecutive import consecutive_cluster

torch.cuda.empty_cache()


def read_scene1_ply(
        filepath: str,
        xyz: bool = True,
        rgb: bool = True,
        semantic: bool = True,
        instance: bool = False,
        remap: bool = False
) -> Data:
    """Read a KITTI-360 window –i.e. a tile– saved as PLY.

    :param filepath: str
        Absolute path to the PLY file
    :param xyz: bool
        Whether XYZ coordinates should be saved in the output Data.pos
    :param rgb: bool
        Whether RGB colors should be saved in the output Data.rgb
    :param semantic: bool
        Whether semantic labels should be saved in the output Data.y
    :param instance: bool
        Whether instance labels should be saved in the output Data.obj
    :param remap: bool
        Whether semantic labels should be mapped from their KITTI-360 ID
        to their train ID. For more details, see:
        https://github.com/autonomousvision/kitti360Scripts/blob/master/kitti360scripts/evaluation/semantic_3d/evalPointLevelSemanticLabeling.py
    """
    data = Data()
    with open(filepath, "rb") as f:
        head = f.read(100)
        print(head)
    with open(filepath, "rb") as f:
        window = PlyData.read(f)
        attributes = [p.name for p in window['vertex'].properties]

        if xyz:
            pos = torch.stack([
                torch.FloatTensor(window["vertex"][axis].astype(np.float32))
                for axis in ["x", "y", "z"]], dim=-1)
            pos_offset = pos[0]
            data.pos = pos - pos_offset
            data.pos_offset = pos_offset

        if rgb:
            data.rgb = to_float_rgb(torch.stack([
                torch.FloatTensor(window["vertex"][axis].astype(np.float32))
                for axis in ["red", "green", "blue"]], dim=-1))

        if semantic and 'semantic' in attributes:
            y = torch.LongTensor(window["vertex"]['semantic'])
            data.y = torch.from_numpy(ID2TRAINID)[y] if remap else y

        if instance and 'instance' in attributes:
            idx = torch.arange(data.num_points)
            obj = torch.LongTensor(window["vertex"]['instance'])
            # is_stuff = obj % 1000 == 0
            # obj[is_stuff] = 0
            obj = consecutive_cluster(obj)[0]
            count = torch.ones_like(obj)
            y = torch.LongTensor(window["vertex"]['semantic'])
            y = torch.from_numpy(ID2TRAINID)[y] if remap else y
            data.obj = InstanceData(idx, obj, count, y, dense=True)

    return data

def save_xyzrgb_label_to_ply(data, filename):
    import numpy as np

    # 当前label到原始label的映射表
    label_mapping = np.array([7, 8, 11, 12, 13, 17, 19, 20,
                              21, 22, 24, 26, 27, 32, 33, -1])

    # 获取数据，并转为 numpy
    if hasattr(data, 'pos_offset'):
        xyz = (data.pos + data.pos_offset).cpu().numpy()
    else:
        xyz = data.pos.cpu().numpy()
    rgb = data.rgb.cpu().numpy()
    label = data.semantic_pred.cpu().numpy().reshape(-1)

    # 转换 RGB 为 0-255 的 uint8 格式
    if rgb.max() <= 1.0:
        rgb = (rgb * 255).astype(np.uint8)
    else:
        rgb = rgb.astype(np.uint8)

    # 将当前 label 映射为原始 label
    label = label_mapping[label.astype(np.int32)]

    # 拼接数据为 [x, y, z, r, g, b, label]
    all_data = np.concatenate([xyz, rgb, label[:, None]], axis=1)

    num_points = all_data.shape[0]

    # 写入 PLY 文件（ASCII 格式）
    with open(filename, 'w') as f:
        # 写 PLY 头部
        f.write("ply\n")
        f.write("format ascii 1.0\n")
        f.write(f"element vertex {num_points}\n")
        f.write("property float x\n")
        f.write("property float y\n")
        f.write("property float z\n")
        f.write("property uchar red\n")
        f.write("property uchar green\n")
        f.write("property uchar blue\n")
        f.write("property int label\n")
        f.write("end_header\n")

        # 写数据行
        for point in all_data:
            f.write("{:.4f} {:.4f} {:.4f} {} {} {} {}\n".format(
                point[0], point[1], point[2],
                int(point[3]), int(point[4]), int(point[5]),
                int(point[6])
            ))

    print(f"保存成功: {filename} （共 {num_points} 点）")

def predict_single_ply(input_path, output_path, model, cfg, transforms_dict):
    """
    对单个 PLY 文件进行语义分割预测并保存为新的 PLY 文件

    :param input_path: str，输入的 .ply 文件路径
    :param output_path: str，保存预测结果的路径
    :param model: Superpoint Transformer 已加载模型
    :param cfg: hydra 配置对象
    :param transforms_dict: 数据预处理和增强字典
    """
    print(f"Predicting: {input_path}")
    
    # 读取点云
    data = read_scene1_ply(input_path)

    # 数据预处理（同训练流程）
    nag = transforms_dict['pre_transform'](data)
    nag = NAGRemoveKeys(level=0, keys=[k for k in nag[0].keys if k not in cfg.datamodule.point_load_keys])(nag)
    nag = NAGRemoveKeys(level='1+', keys=[k for k in nag[1].keys if k not in cfg.datamodule.segment_load_keys])(nag)
    nag = nag.cuda()
    nag = transforms_dict['on_device_test_transform'](nag)

    # 模型推理
    with torch.no_grad():
        output = model(nag)

    # 生成 full-res 预测标签
    nag[0].semantic_pred = output.voxel_semantic_pred(super_index=nag[0].super_index)
    raw_semseg_y = output.full_res_semantic_pred(
        super_index_level0_to_level1=nag[0].super_index,
        sub_level0_to_raw=nag[0].sub
    )
    data.semantic_pred = raw_semseg_y

    # 保存为新的PLY文件
    save_xyzrgb_label_to_ply(data, output_path)
    print(f"Saved: {output_path}")

def predict_folder_ply(input_folder, model, cfg, transforms_dict):
    """
    对文件夹下所有 .ply 文件进行预测，并将结果保存为 *_p.ply，同时输出进度
    """
    # 收集所有待处理的文件
    ply_paths = []
    for root, _, files in os.walk(input_folder):
        for file in files:
            if file.endswith('.ply') and not file.endswith('_p.ply'):
                ply_paths.append(os.path.join(root, file))

    total = len(ply_paths)
    if total == 0:
        print("❗ 未找到任何需要处理的 .ply 文件。")
        return

    print(f"📦 共找到 {total} 个 PLY 文件，开始逐个处理...\n")

    for i, input_path in enumerate(ply_paths, 1):
        output_path = input_path[:-4] + '_p.ply'
        print(f"[{i}/{total}] 🛠️ 处理文件: {os.path.basename(input_path)} ({i / total * 100:.1f}%)")
        try:
            predict_single_ply(input_path, output_path, model, cfg, transforms_dict)
        except Exception as e:
            print(f"❌ 错误: {input_path}\n    原因: {e}")
        print("-" * 60)

    print("\n✅ 所有处理完成！")


if __name__ == '__main__':
    input_folder = "/home/<USER>/文档/pc_ss/superpoint_transformer-master/data/guitu_dsp"  # 替换为你的目录路径

    # 初始化配置与模型（只需一次）
    cfg = init_config(overrides=[
        f"experiment=semantic/kitti360",
        f"datamodule.load_full_res_idx={True}"
    ])
    transforms_dict = instantiate_datamodule_transforms(cfg.datamodule)

    ckpt_path = "/home/<USER>/文档/pc_ss/superpoint_transformer-master/ckpt/mytrain_kitti360/epoch_149.ckpt"
    model = hydra.utils.instantiate(cfg.model)
    model = model._load_from_checkpoint(ckpt_path)
    model = model.eval().cuda()

    # 开始处理整个文件夹
    predict_folder_ply(input_folder, model, cfg, transforms_dict)




'''
filepath = '/home/<USER>/文档/pc_ss/superpoint_transformer-master/data/xgrid/road2_dsp.ply'
data = read_scene1_ply(filepath)

cfg = init_config(overrides=[
    f"experiment=semantic/kitti360",
    f"datamodule.load_full_res_idx={True}"  # only when you need full-resolution predictions 
])
transforms_dict = instantiate_datamodule_transforms(cfg.datamodule)

# Apply pre-transforms
nag = transforms_dict['pre_transform'](data)

# Simulate the behavior of the dataset's I/O behavior with only
# `point_load_keys` and `segment_load_keys` loaded from disk
nag = NAGRemoveKeys(level=0, keys=[k for k in nag[0].keys if k not in cfg.datamodule.point_load_keys])(nag)
nag = NAGRemoveKeys(level='1+', keys=[k for k in nag[1].keys if k not in cfg.datamodule.segment_load_keys])(nag)

# Move to device
nag = nag.cuda()

# Apply on-device transforms
nag = transforms_dict['on_device_test_transform'](nag)

# Path to the checkpoint file downloaded from https://zenodo.org/records/8042712
# ckpt_path = "/home/<USER>/文档/pc_ss/superpoint_transformer-master/ckpt/spt-2_kitti360.ckpt"
ckpt_path = "/home/<USER>/文档/pc_ss/superpoint_transformer-master/ckpt/mytrain_kitti360/epoch_149.ckpt"

# Instantiate the model and load pretrained weights
model = hydra.utils.instantiate(cfg.model)
model = model._load_from_checkpoint(ckpt_path)

# Set the model in inference mode on the same device as the input
model = model.eval().to(nag.device)

# Inference, returns a task-specific ouput object carrying predictions
with torch.no_grad():
    output = model(nag)

# Compute the level-0 (voxel-wise) semantic segmentation predictions 
# based on the predictions on level-1 superpoints and save those for 
# visualization in the level-0 Data under the 'semantic_pred' attribute
nag[0].semantic_pred = output.voxel_semantic_pred(super_index=nag[0].super_index)

# Compute the full-resolution semantic prediction. These labels are ordered 
# with respect to the full-resolution data points in the corresponding raw 
# input file. Note that we do not provide the pipeline for recovering the 
# corresponding full-resolution positions, colors, etc. 
raw_semseg_y = output.full_res_semantic_pred(
    super_index_level0_to_level1=nag[0].super_index,
    sub_level0_to_raw=nag[0].sub)
data.semantic_pred = raw_semseg_y

save_xyzrgb_label_to_ply(data, '/home/<USER>/文档/pc_ss/superpoint_transformer-master/data/xgrid/road2_dsp_p.ply')

'''
