import numpy as np
from plyfile import PlyData, PlyElement
from scipy.spatial import cKDTree

# ---------- 读取 RandLA-Net 点云 ----------
randla_ply = PlyData.read("/home/<USER>/文档/pc_ss/RandLA-Net-master/test/map1_subcloud_3.ply")
randla_data = randla_ply['vertex'].data
randla_xyz = np.stack([randla_data['x'], randla_data['y'], randla_data['z']], axis=-1)
randla_preds = np.array(randla_data['preds'])

# ---------- 读取 SPT 点云 ----------
spt_ply = PlyData.read("/home/<USER>/文档/pc_ss/superpoint_transformer-master/data/scene2/map1_subcloud_3_predict.ply")
spt_data = spt_ply['vertex'].data
spt_xyz = np.stack([spt_data['x'], spt_data['y'], spt_data['z']], axis=-1)
spt_array = np.array(spt_data.tolist())
spt_dtype = spt_data.dtype

# ---------- 构建 SPT 的 KDTree ----------
spt_kdtree = cKDTree(spt_xyz)

# ---------- 找出 RandLA-Net 中 preds 为 2 和 5 的点 ----------
mask_2 = randla_preds == 2
mask_5 = randla_preds == 5

target_points = np.concatenate([randla_xyz[mask_2], randla_xyz[mask_5]], axis=0)
target_labels = np.concatenate([np.full(np.sum(mask_2), 45), np.full(np.sum(mask_5), 46)])

# ---------- 在 SPT 中找到最近邻 ----------
distances, indices = spt_kdtree.query(target_points, k=1)

# ---------- 更新 SPT 中对应点的标签 ----------
label_index = spt_dtype.names.index('label')
for idx, new_label in zip(indices, target_labels):
    spt_array[idx, label_index] = new_label

# ---------- 保存修改后的 SPT ----------
modified_data = np.array([tuple(row) for row in spt_array], dtype=spt_dtype)
el = PlyElement.describe(modified_data, 'vertex')
PlyData([el], text=False).write("map1_subcloud_3_merged.ply")
