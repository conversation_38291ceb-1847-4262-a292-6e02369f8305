import re
from plyfile import PlyData, PlyElement
import numpy as np

def clean_ply_binary_header(input_path, output_path):
    with open(input_path, 'rb') as f:
        content = f.read()

    # 查找 header 结束的位置（以 b'end_header\n' 为界）
    header_end_index = content.find(b'end_header\n')
    if header_end_index == -1:
        raise ValueError("Invalid PLY file: missing end_header")

    # 提取 header 和数据部分
    header_bytes = content[:header_end_index + len(b'end_header\n')]
    body_bytes = content[header_end_index + len(b'end_header\n'):]

    # 尝试以 latin1 解码 header（保留所有原始字节）
    header_text = header_bytes.decode('latin1')
    header_lines = header_text.splitlines()

    # 正则过滤掉包含非 ASCII 的 comment 行
    clean_lines = []
    for line in header_lines:
        if line.startswith("comment"):
            # 若包含非 ASCII 字符，就跳过这一行
            if re.search(r'[^\x00-\x7F]', line):
                continue
        clean_lines.append(line)

    # 构造新的 header（注意 \n 结尾）
    new_header_text = '\n'.join(clean_lines) + '\n'
    new_header_bytes = new_header_text.encode('ascii')  # 全部是 ASCII，安全！

    # 拼接 header 和 binary 数据
    cleaned_content = new_header_bytes + body_bytes

    # 写入新的 .ply 文件
    with open(output_path, 'wb') as f_out:
        f_out.write(cleaned_content)

    print(f"✅ Cleaned PLY saved to: {output_path}")

def shift_ply_xyz_with_color(input_path, output_path, dx=15000.0, dy=33000.0):
    # 打开并读取 PLY 数据
    with open(input_path, 'rb') as f:
        plydata = PlyData.read(f)

    # 获取 vertex 数据
    vertex_data = plydata['vertex'].data

    # 获取字段名，确认包含 RGB
    expected_fields = ['x', 'y', 'z', 'red', 'green', 'blue']
    if not all(field in vertex_data.dtype.names for field in expected_fields):
        raise ValueError("PLY vertex data must contain x, y, z, red, green, blue fields.")

    # 修改 x 和 y 坐标
    new_vertices = np.empty(vertex_data.shape, dtype=vertex_data.dtype)
    new_vertices[:] = vertex_data
    new_vertices['x'] += dx
    new_vertices['y'] += dy

    # 重新构建 PlyElement 并写入文件
    new_vertex_element = PlyElement.describe(new_vertices, 'vertex')
    PlyData([new_vertex_element], text=False).write(output_path)

    print(f"✅ 已成功平移: {output_path}")



input_path1 = "/home/<USER>/Data/ytj/guitu/测试/ply_xyzrgb/20240731104636477.ply"
output_path1 = "/home/<USER>/Data/ytj/guitu/测试/ply_xyzrgb/20240731104636477_p.ply"
clean_ply_binary_header(input_path1, output_path1)

#input_path2 = "/home/<USER>/Data/ytj/spt_xuancheng/subcloud_2_2_p.ply"
#output_path2 = "/home/<USER>/Data/ytj/spt_xuancheng/subcloud_2_2_s.ply"
#shift_ply_xyz_with_color(input_path2, output_path2, dx=15000.0, dy=33000.0)