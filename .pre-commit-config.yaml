default_language_version:
  python: python3

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      # list of supported hooks: https://pre-commit.com/hooks.html
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-docstring-first
      - id: check-yaml
      - id: debug-statements
      - id: detect-private-key
      - id: check-executables-have-shebangs
      - id: check-toml
      - id: check-case-conflict
      - id: check-added-large-files

  # python code formatting
  - repo: https://github.com/psf/black
    rev: 22.6.0
    hooks:
      - id: black
        args: [--line-length, "99"]

  # python import sorting
  - repo: https://github.com/PyCQA/isort
    rev: 5.10.1
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]

  # python upgrading syntax to newer version
  - repo: https://github.com/asottile/pyupgrade
    rev: v2.32.1
    hooks:
      - id: pyupgrade
        args: [--py38-plus]

  # python docstring formatting
  - repo: https://github.com/myint/docformatter
    rev: v1.4
    hooks:
      - id: docformatter
        args: [--in-place, --wrap-summaries=99, --wrap-descriptions=99]

  # python check (PEP8), programming errors and code complexity
  - repo: https://github.com/PyCQA/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
        args:
          [
            "--extend-ignore",
            "E203,E402,E501,F401,F841",
            "--exclude",
            "logs/*,data/*",
          ]

  # python security linter
  - repo: https://github.com/PyCQA/bandit
    rev: "1.7.1"
    hooks:
      - id: bandit
        args: ["-s", "B101"]

  # yaml formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v2.7.1
    hooks:
      - id: prettier
        types: [yaml]

  # shell scripts linter
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.8.0.4
    hooks:
      - id: shellcheck

  # md formatting
  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.14
    hooks:
      - id: mdformat
        args: ["--number"]
        additional_dependencies:
          - mdformat-gfm
          - mdformat-tables
          - mdformat_frontmatter
          # - mdformat-toc
          # - mdformat-black

  # word spelling linter
  - repo: https://github.com/codespell-project/codespell
    rev: v2.1.0
    hooks:
      - id: codespell
        args:
          - --skip=logs/**,data/**,*.ipynb
          # - --ignore-words-list=abc,def

  # jupyter notebook cell output clearing
  - repo: https://github.com/kynan/nbstripout
    rev: 0.5.0
    hooks:
      - id: nbstripout

  # jupyter notebook linting
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.4.0
    hooks:
      - id: nbqa-black
        args: ["--line-length=99"]
      - id: nbqa-isort
        args: ["--profile=black"]
      - id: nbqa-flake8
        args:
          [
            "--extend-ignore=E203,E402,E501,F401,F841",
            "--exclude=logs/*,data/*",
          ]
