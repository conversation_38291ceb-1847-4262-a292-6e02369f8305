name: Tests

on:
  push:
    branches: [main]
  pull_request:
    branches: [main, "release/*"]

jobs:
  run_tests:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        os: ["ubuntu-latest", "macos-latest"]
        python-version: ["3.7", "3.8", "3.9", "3.10"]

    timeout-minutes: 10

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest
          pip install sh

      - name: List dependencies
        run: |
          python -m pip list

      - name: Run pytest
        run: |
          pytest -v

  run_tests_windows:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        os: ["windows-latest"]
        python-version: ["3.7", "3.8", "3.9", "3.10"]

    timeout-minutes: 10

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest

      - name: List dependencies
        run: |
          python -m pip list

      - name: Run pytest
        run: |
          pytest -v

  # upload code coverage report
  code-coverage:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Python 3.10
        uses: actions/setup-python@v2
        with:
          python-version: "3.10"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest
          pip install pytest-cov[toml]
          pip install sh

      - name: Run tests and collect coverage
        run: pytest --cov src # NEEDS TO BE UPDATED WHEN CHANGING THE NAME OF "src" FOLDER

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
