# Same as `code-quality-pr.yaml` but triggered on commit to main branch
# and runs on all files (instead of only the changed ones)

name: Code Quality Main

on:
  push:
    branches: [main]

jobs:
  code-quality:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2

      - name: Run pre-commits
        uses: pre-commit/action@v2.0.3
