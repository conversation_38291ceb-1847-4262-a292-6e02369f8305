# This workflow finds which files were changed, prints them,
# and runs `pre-commit` on those files.

# Inspired by the sktime library:
# https://github.com/alan-turing-institute/sktime/blob/main/.github/workflows/test.yml

name: Code Quality PR

on:
  pull_request:
    branches: [main, "release/*"]

jobs:
  code-quality:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2

      - name: Find modified files
        id: file_changes
        uses: trilom/file-changes-action@v1.2.4
        with:
          output: " "

      - name: List modified files
        run: echo '${{ steps.file_changes.outputs.files}}'

      - name: Run pre-commits
        uses: pre-commit/action@v2.0.3
        with:
          extra_args: --files ${{ steps.file_changes.outputs.files}}
