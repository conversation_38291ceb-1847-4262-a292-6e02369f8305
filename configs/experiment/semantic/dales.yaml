# @package _global_

# to execute this experiment run:
# python train.py experiment=semantic/dales

defaults:
  - override /datamodule: semantic/dales.yaml
  - override /model: semantic/spt-2.yaml
  - override /trainer: gpu.yaml

# all parameters below will be merged with parameters from default configurations set above
# this allows you to overwrite only specified parameters

trainer:
  max_epochs: 400

model:
  optimizer:
    lr: 0.01
    weight_decay: 1e-4

logger:
  wandb:
    project: "spt_dales"
    name: "SPT-64"