# @package _global_

# to execute this experiment run:
# python train.py experiment=semantic/kitti360

defaults:
  - override /datamodule: semantic/kitti360.yaml
  - override /model: semantic/spt-2.yaml
  - override /trainer: gpu.yaml

# all parameters below will be merged with parameters from default configurations set above
# this allows you to overwrite only specified parameters

trainer:
  max_epochs: 200

model:
  optimizer:
    lr: 0.01
    weight_decay: 1e-4

  _down_dim: [ 128, 128, 128, 128 ]
  _up_dim: [ 128, 128, 128 ]

  net:
    no_ffn: False
    down_ffn_ratio: 1

logger:
  wandb:
    project: "spt_kitti360"
    name: "SPT-128"
