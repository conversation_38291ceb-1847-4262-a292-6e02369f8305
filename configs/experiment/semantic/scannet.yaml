# @package _global_

# to execute this experiment run:
# python train.py experiment=semantic/scannet

defaults:
  - override /datamodule: semantic/scannet.yaml
  - override /model: semantic/spt-2.yaml
  - override /trainer: gpu.yaml

# all parameters below will be merged with parameters from default configurations set above
# this allows you to overwrite only specified parameters

trainer:
  max_epochs: 100
  check_val_every_n_epoch: 2

model:
  optimizer:
    lr: 0.01
    weight_decay: 1e-4

  scheduler:
    num_warmup: 2

  _node_mlp_out: 64
  _h_edge_mlp_out: 64
  _down_dim: [ 128, 128, 128, 128 ]
  _up_dim: [ 128, 128, 128 ]
  net:
    no_ffn: False
    down_ffn_ratio: 1
    down_num_heads: 32


logger:
  wandb:
    project: "spt_scannet"
    name: "SPT-128"
