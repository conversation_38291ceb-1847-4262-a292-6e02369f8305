# @package _global_

# to execute this experiment run:
# python train.py experiment=semantic/dales_nano

defaults:
  - override /datamodule: semantic/dales_nano.yaml
  - override /model: semantic/nano-2.yaml
  - override /trainer: gpu.yaml

# all parameters below will be merged with parameters from default configurations set above
# this allows you to overwrite only specified parameters

trainer:
  max_epochs: 400

model:
  optimizer:
    lr: 0.01
    weight_decay: 1e-4

logger:
  wandb:
    project: "spt_dales"
    name: "NANO"
