# @package model
defaults:
  - /model/semantic/default.yaml

# Partial spt config specifically for the encoder
net:
  down_dim: ${model._down_dim}
  down_pool_dim: ${eval:'[${model._point_mlp}[-1]] + ${model._down_dim}[:-1]'}
  down_in_mlp: ${eval:'[ [${model._node_injection_dim} + ${model._point_mlp}[-1] * (not ${model.net.nano}) + ${datamodule.num_hf_segment} * (${model.net.nano} and not ${model.net.use_node_hf})] + [${model._down_dim}[0]] * ${model._mlp_depth}, [${model._node_injection_dim} + ${model._down_dim}[0]] + [${model._down_dim}[1]] * ${model._mlp_depth}, [${model._node_injection_dim} + ${model._down_dim}[1]] + [${model._down_dim}[2]] * ${model._mlp_depth}, [${model._node_injection_dim} + ${model._down_dim}[2]] + [${model._down_dim}[3]] * ${model._mlp_depth} ]'}
  down_out_mlp: null
  down_mlp_drop: null
  down_num_heads: 16
  down_num_blocks: 3
  down_ffn_ratio: 1
  down_residual_drop: null
  down_attn_drop: null
  down_drop_path: null
