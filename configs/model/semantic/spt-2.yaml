# @package model
defaults:
  - /model/semantic/spt.yaml

net:
  down_dim: ${eval:'${model._down_dim}[:2]'}
  down_in_mlp: ${eval:'[ [${model._node_injection_dim} + ${model._point_mlp}[-1] * (not ${model.net.nano}) + ${datamodule.num_hf_segment} * (${model.net.nano} and not ${model.net.use_node_hf})] + [${model._down_dim}[0]] * ${model._mlp_depth}, [${model._node_injection_dim} + ${model._down_dim}[0]] + [${model._down_dim}[1]] * ${model._mlp_depth} ]'}
  up_dim: ${eval:'${model._up_dim}[:1]'}
  up_in_mlp: ${eval:'[ [${model._node_injection_dim} + ${model._down_dim}[-1] + ${model._down_dim}[-2]] + [${model._up_dim}[0]] * ${model._mlp_depth} ]'}
