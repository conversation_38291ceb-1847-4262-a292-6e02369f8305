# @package model
defaults:
  - /model/semantic/default.yaml
  - /model/semantic/_point.yaml
  - /model/semantic/_down.yaml
  - /model/semantic/_up.yaml
  - /model/semantic/_attention.yaml

net:
  _target_: src.models.components.spt.SPT

  nano: False
  node_mlp: ${eval:'[${model._node_hf_dim}] + [${model._node_mlp_out}] * ${model._mlp_depth} if ${model._node_mlp_out} and ${model.net.use_node_hf} and ${model._node_hf_dim} > 0 else None'}
  h_edge_mlp: ${eval:'[${model._h_edge_hf_dim}] + [${model._h_edge_mlp_out}] * ${model._mlp_depth} if ${model._h_edge_mlp_out} else None'}
  v_edge_mlp: ${eval:'[${model._v_edge_hf_dim}] + [${model._v_edge_mlp_out}] * ${model._mlp_depth} if ${model._v_edge_mlp_out} else None'}
  share_hf_mlps: False
  mlp_activation:
    _target_: torch.nn.LeakyReLU
  mlp_norm:
    _target_: src.nn.GraphNorm
    _partial_: True

  use_pos: True  # whether features should include position (with unit-sphere normalization wrt siblings)
  use_node_hf: True  # whether features should include node handcrafted features (after optional node_mlp, if features are actually loaded by the datamodule)
  use_diameter: False  # whether features should include the superpoint's diameter (from unit-sphere normalization wrt siblings)
  use_diameter_parent: True  # whether features should include diameter of the superpoint's parent (from unit-sphere normalization wrt siblings)
  pool: 'max'  # pooling across the cluster, supports 'max', 'mean', 'min'
  unpool: 'index'
  fusion: 'cat'
  norm_mode: 'graph'
